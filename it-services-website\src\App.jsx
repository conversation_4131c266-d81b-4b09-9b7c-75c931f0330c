import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import './App.css'
import Header from './components/Header'
import Hero from './components/Hero'
import Services from './components/Services'
import About from './components/About'
import Contact from './components/Contact'
import Footer from './components/Footer'
import Microsoft365Page from './pages/Microsoft365Page'
import WebsitePage from './pages/WebsitePage'
import AIAutomationPage from './pages/AIAutomationPage'

function HomePage() {
  return (
    <>
      <Hero />
      <Services />
      <About />
      <Contact />
    </>
  )
}

function App() {
  return (
    <Router>
      <div className="App">
        <Header />
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/services/microsoft365" element={<Microsoft365Page />} />
          <Route path="/services/website-building" element={<WebsitePage />} />
          <Route path="/services/ai-automation" element={<AIAutomationPage />} />
        </Routes>
        <Footer />
      </div>
    </Router>
  )
}

export default App
