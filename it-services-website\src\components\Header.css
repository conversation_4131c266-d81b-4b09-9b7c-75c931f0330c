.header {
  background: #fff;
  box-shadow: 0 2px 10px rgba(0,0,0,0.15);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 1rem 0;
  border-bottom: 1px solid #e0e0e0;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-brand a {
  text-decoration: none;
}

.nav-brand h2 {
  color: #1a1a1a;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.nav-list a {
  text-decoration: none;
  color: #2c2c2c;
  font-weight: 500;
  transition: color 0.3s ease;
  cursor: pointer;
}

.nav-list a:hover {
  color: #000;
}

.menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.menu-toggle span {
  width: 25px;
  height: 3px;
  background: #2c2c2c;
  transition: 0.3s;
}

@media (max-width: 768px) {
  .menu-toggle {
    display: flex;
  }

  .nav {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transform: translateY(-100%);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
  }

  .nav-open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
  }

  .nav-list {
    flex-direction: column;
    padding: 2rem;
    gap: 1rem;
  }

  .nav-list a {
    font-size: 1.1rem;
  }
}
