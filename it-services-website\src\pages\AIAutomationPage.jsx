import React from 'react'
import { Link } from 'react-router-dom'
import './ServicePage.css'

const AIAutomationPage = () => {
  const services = [
    {
      title: 'Process Automation',
      icon: '🔄',
      description: 'Automate repetitive business processes to save time and reduce errors',
      details: [
        'Workflow automation design',
        'Document processing automation',
        'Data entry and validation',
        'Report generation automation',
        'Email and notification systems',
        'Integration with existing systems'
      ],
      examples: [
        'Invoice processing and approval',
        'Customer onboarding workflows',
        'Inventory management automation',
        'HR processes and payroll',
        'Quality control procedures'
      ]
    },
    {
      title: 'Chatbot Development',
      icon: '🤖',
      description: 'Intelligent chatbots for customer service and internal support',
      details: [
        'Natural language processing',
        'Multi-platform deployment',
        'Integration with knowledge bases',
        'Escalation to human agents',
        'Analytics and performance tracking',
        'Continuous learning capabilities'
      ],
      examples: [
        'Customer support chatbots',
        'FAQ and help desk automation',
        'Lead qualification bots',
        'Internal IT support bots',
        'E-commerce shopping assistants'
      ]
    },
    {
      title: 'Data Analysis & Insights',
      icon: '📊',
      description: 'AI-powered analytics to extract valuable insights from your data',
      details: [
        'Automated data collection',
        'Pattern recognition and analysis',
        'Custom dashboard creation',
        'Real-time monitoring systems',
        'Anomaly detection',
        'Predictive modeling'
      ],
      examples: [
        'Sales performance analytics',
        'Customer behavior analysis',
        'Financial forecasting',
        'Operational efficiency metrics',
        'Market trend analysis'
      ]
    },
    {
      title: 'Document Processing',
      icon: '📄',
      description: 'Intelligent document processing and information extraction',
      details: [
        'OCR and text extraction',
        'Document classification',
        'Data validation and verification',
        'Automated filing and organization',
        'Content summarization',
        'Multi-language support'
      ],
      examples: [
        'Contract analysis and extraction',
        'Invoice processing automation',
        'Resume screening and parsing',
        'Legal document review',
        'Medical record processing'
      ]
    }
  ]

  const benefits = [
    {
      icon: '⏰',
      title: 'Time Savings',
      description: 'Reduce manual work by up to 80% with intelligent automation solutions'
    },
    {
      icon: '🎯',
      title: 'Improved Accuracy',
      description: 'Eliminate human errors and ensure consistent, reliable results'
    },
    {
      icon: '💡',
      title: 'Better Insights',
      description: 'Uncover hidden patterns and trends in your business data'
    },
    {
      icon: '📈',
      title: 'Scalable Growth',
      description: 'Handle increased workload without proportional staff increases'
    }
  ]

  const aiTools = [
    { name: 'OpenAI GPT', icon: '🧠', description: 'Advanced language models for text processing' },
    { name: 'Microsoft AI', icon: '🔷', description: 'Cognitive services and machine learning' },
    { name: 'Google AI', icon: '🔍', description: 'Cloud AI and machine learning platforms' },
    { name: 'Python/TensorFlow', icon: '🐍', description: 'Custom AI model development' },
    { name: 'Power Platform AI', icon: '⚡', description: 'AI Builder and cognitive services' },
    { name: 'Zapier/Make', icon: '🔗', description: 'No-code automation platforms' }
  ]

  const useCases = [
    {
      industry: 'Healthcare',
      icon: '🏥',
      applications: [
        'Patient record processing',
        'Appointment scheduling automation',
        'Medical image analysis',
        'Drug interaction checking'
      ]
    },
    {
      industry: 'Finance',
      icon: '💰',
      applications: [
        'Fraud detection systems',
        'Credit risk assessment',
        'Automated trading algorithms',
        'Compliance monitoring'
      ]
    },
    {
      industry: 'Retail',
      icon: '🛍️',
      applications: [
        'Inventory optimization',
        'Price optimization',
        'Customer recommendation engines',
        'Supply chain automation'
      ]
    },
    {
      industry: 'Manufacturing',
      icon: '🏭',
      applications: [
        'Quality control automation',
        'Predictive maintenance',
        'Production optimization',
        'Supply chain management'
      ]
    }
  ]

  return (
    <div className="service-page">
      {/* Hero Section */}
      <section className="service-hero">
        <div className="container">
          <div className="breadcrumb">
            <Link to="/">Home</Link> / <span>AI Automation Solutions</span>
          </div>
          <h1>AI Automation Solutions</h1>
          <p className="hero-description">
            Harness the power of artificial intelligence to automate processes, gain insights, and transform your business operations. 
            Our AI solutions help you work smarter, not harder.
          </p>
        </div>
      </section>

      {/* Services Detail Section */}
      <section className="services-detail">
        <div className="container">
          <h2>Our AI Automation Services</h2>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-detail-card">
                <div className="service-header">
                  <span className="service-icon">{service.icon}</span>
                  <h3>{service.title}</h3>
                </div>
                <p className="service-description">{service.description}</p>
                
                <div className="service-content">
                  <div className="service-details">
                    <h4>What We Deliver:</h4>
                    <ul>
                      {service.details.map((detail, idx) => (
                        <li key={idx}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="service-examples">
                    <h4>Use Cases:</h4>
                    <ul>
                      {service.examples.map((example, idx) => (
                        <li key={idx}>{example}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="benefits-section">
        <div className="container">
          <h2>Benefits of AI Automation</h2>
          <div className="benefits-grid">
            {benefits.map((benefit, index) => (
              <div key={index} className="benefit-card">
                <div className="benefit-icon">{benefit.icon}</div>
                <h3>{benefit.title}</h3>
                <p>{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* AI Tools Section */}
      <section className="technologies-section">
        <div className="container">
          <h2>AI Technologies We Use</h2>
          <div className="technologies-grid">
            {aiTools.map((tool, index) => (
              <div key={index} className="tech-card">
                <div className="tech-icon">{tool.icon}</div>
                <h3>{tool.name}</h3>
                <p>{tool.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Industry Use Cases */}
      <section className="use-cases-section">
        <div className="container">
          <h2>Industry Applications</h2>
          <div className="use-cases-grid">
            {useCases.map((useCase, index) => (
              <div key={index} className="use-case-card">
                <div className="use-case-icon">{useCase.icon}</div>
                <h3>{useCase.industry}</h3>
                <ul>
                  {useCase.applications.map((app, idx) => (
                    <li key={idx}>{app}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <h2>Ready to Automate Your Business?</h2>
          <p>Let's explore how AI can transform your operations and drive efficiency.</p>
          <div className="cta-buttons">
            <Link to="/#contact" className="btn btn-primary">Get Started</Link>
            <Link to="/" className="btn btn-secondary">Back to Home</Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default AIAutomationPage
