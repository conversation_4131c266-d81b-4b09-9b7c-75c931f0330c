import React from 'react'
import './Hero.css'

const Hero = () => {
  const scrollToServices = () => {
    const element = document.getElementById('services')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  const scrollToContact = () => {
    const element = document.getElementById('contact')
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }

  return (
    <section id="home" className="hero">
      <div className="hero-background">
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title">
              Transform Your Business with 
              <span className="highlight"> Modern IT Solutions</span>
            </h1>
            <p className="hero-subtitle">
              We provide comprehensive IT services including SharePoint Online, Power Apps, 
              custom website development, and AI automation to help your business thrive in the digital age.
            </p>
            <div className="hero-buttons">
              <button className="btn btn-primary" onClick={scrollToServices}>
                Our Services
              </button>
              <button className="btn btn-secondary" onClick={scrollToContact}>
                Get Started
              </button>
            </div>
          </div>
          <div className="hero-features">
            <div className="feature-item">
              <div className="feature-icon">🚀</div>
              <h3>Fast Deployment</h3>
              <p>Quick implementation of your IT solutions</p>
            </div>
            <div className="feature-item">
              <div className="feature-icon">🔧</div>
              <h3>Expert Support</h3>
              <p>Professional guidance every step of the way</p>
            </div>
            <div className="feature-item">
              <div className="feature-icon">📈</div>
              <h3>Scalable Solutions</h3>
              <p>Technology that grows with your business</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
