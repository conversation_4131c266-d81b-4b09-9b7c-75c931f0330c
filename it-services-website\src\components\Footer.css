.footer {
  background: #2c3e50;
  color: white;
  padding: 60px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3 {
  color: #ffd700;
  font-size: 1.8rem;
  margin-bottom: 20px;
}

.footer-section h4 {
  color: #ffd700;
  font-size: 1.3rem;
  margin-bottom: 20px;
}

.footer-section p {
  line-height: 1.6;
  color: #bdc3c7;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #bdc3c7;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #ffd700;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #34495e;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #ffd700;
  transform: translateY(-3px);
}

.contact-info p {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-bottom {
  border-top: 1px solid #34495e;
  padding: 30px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #bdc3c7;
  margin: 0;
}

.back-to-top {
  background: #ffd700;
  color: #2c3e50;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-top:hover {
  background: #ffed4e;
  transform: translateY(-3px);
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}
