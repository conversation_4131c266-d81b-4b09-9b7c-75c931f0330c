.footer {
  background: #1a1a1a;
  color: white;
  padding: 60px 0 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3 {
  color: #c0c0c0;
  font-size: 1.8rem;
  margin-bottom: 20px;
}

.footer-section h4 {
  color: #c0c0c0;
  font-size: 1.3rem;
  margin-bottom: 20px;
}

.footer-section p {
  line-height: 1.6;
  color: #a0a0a0;
  margin-bottom: 20px;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  color: #a0a0a0;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section ul li a:hover {
  color: #c0c0c0;
}

.social-links {
  display: flex;
  gap: 15px;
  margin-top: 20px;
}

.social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background: #2c2c2c;
  border-radius: 50%;
  text-align: center;
  line-height: 40px;
  font-size: 1.2rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background: #c0c0c0;
  transform: translateY(-3px);
}

.contact-info p {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.footer-bottom {
  border-top: 1px solid #2c2c2c;
  padding: 30px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom p {
  color: #a0a0a0;
  margin: 0;
}

.back-to-top {
  background: #c0c0c0;
  color: #1a1a1a;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-to-top:hover {
  background: #e0e0e0;
  transform: translateY(-3px);
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  .footer-bottom-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}
