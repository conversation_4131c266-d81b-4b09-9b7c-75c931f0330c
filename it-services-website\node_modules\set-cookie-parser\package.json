{"name": "set-cookie-parser", "version": "2.7.1", "description": "Parses set-cookie headers into objects", "homepage": "https://github.com/nfriedly/set-cookie-parser", "repository": "nfriedly/set-cookie-parser", "author": {"name": "<PERSON>", "url": "http://nfriedly.com/"}, "files": ["lib"], "main": "./lib/set-cookie.js", "sideEffects": false, "keywords": ["set-cookie", "set", "cookie", "cookies", "header", "parse", "parser"], "devDependencies": {"eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "mocha": "^10.3.0", "prettier": "^3.2.5", "pretty-quick": "^4.0.0", "sinon": "^17.0.1"}, "scripts": {"lint": "eslint . --ignore-pattern '!.eslintrc.js'", "test": "npm run lint && mocha", "autofix": "npm run lint -- --fix", "precommit": "npm test"}, "license": "MIT", "prettier": {"trailingComma": "es5"}}