import React from 'react'
import './Footer.css'

const Footer = () => {
  const currentYear = new Date().getFullYear()

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h3>BossKing Technologies</h3>
            <p>
              Transforming businesses through innovative IT solutions. 
              Your trusted partner for SharePoint, Power Apps, web development, and AI automation.
            </p>
            <div className="social-links">
              <a href="#" aria-label="LinkedIn">💼</a>
              <a href="#" aria-label="Twitter">🐦</a>
              <a href="#" aria-label="Facebook">📘</a>
              <a href="#" aria-label="Email">📧</a>
            </div>
          </div>

          <div className="footer-section">
            <h4>Services</h4>
            <ul>
              <li><a href="#services">Microsoft 365 Assistance</a></li>
              <li><a href="#services">Website Building</a></li>
              <li><a href="#services">AI Automation</a></li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Company</h4>
            <ul>
              <li><a href="#about">About Us</a></li>
              <li><a href="#contact">Contact</a></li>
              <li><a href="#">Privacy Policy</a></li>
              <li><a href="#">Terms of Service</a></li>
            </ul>
          </div>

          <div className="footer-section">
            <h4>Contact Info</h4>
            <div className="contact-info">
              <p>📧 <EMAIL></p>
              <p>📞 +1 (555) 123-4567</p>
              <p>📍 Serving clients worldwide</p>
            </div>
          </div>
        </div>

        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; {currentYear} BossKing Technologies. All rights reserved.</p>
            <button className="back-to-top" onClick={scrollToTop} aria-label="Back to top">
              ↑
            </button>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
