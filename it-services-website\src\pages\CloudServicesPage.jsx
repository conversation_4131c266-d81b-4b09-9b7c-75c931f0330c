import React from 'react'
import { Link } from 'react-router-dom'
import './ServicePage.css'

const CloudServicesPage = () => {
  const services = [
    {
      title: 'AWS Cloud Solutions',
      icon: '🟠',
      description: 'Comprehensive Amazon Web Services solutions for scalable, reliable cloud infrastructure',
      details: [
        'EC2 & Auto Scaling Groups',
        'S3 Storage & CloudFront CDN',
        'RDS & DynamoDB Databases',
        'Lambda Serverless Functions',
        'VPC & Security Groups',
        'CloudWatch Monitoring',
        'IAM Security Management',
        'Cost Optimization Strategies'
      ],
      examples: [
        'Web application hosting',
        'Data lake implementations',
        'Microservices architecture',
        'Disaster recovery solutions',
        'DevOps CI/CD pipelines'
      ]
    },
    {
      title: 'Microsoft Azure Services',
      icon: '🔷',
      description: 'Enterprise-grade Microsoft Azure cloud solutions integrated with your existing Microsoft ecosystem',
      details: [
        'Azure Virtual Machines & Scale Sets',
        'Azure Storage & Blob Services',
        'Azure SQL Database & Cosmos DB',
        'Azure Functions & Logic Apps',
        'Azure Active Directory Integration',
        'Azure Monitor & Application Insights',
        'Azure DevOps & GitHub Actions',
        'Hybrid Cloud Solutions'
      ],
      examples: [
        'Enterprise application migration',
        'Microsoft 365 integration',
        'Hybrid identity solutions',
        'Azure-based data analytics',
        'Container orchestration with AKS'
      ]
    },
    {
      title: 'Cloud Migration & Strategy',
      icon: '🚀',
      description: 'Strategic cloud migration planning and execution to modernize your IT infrastructure',
      details: [
        'Cloud readiness assessment',
        'Migration strategy development',
        'Application modernization',
        'Data migration planning',
        'Security & compliance review',
        'Performance optimization',
        'Staff training & knowledge transfer',
        'Post-migration support'
      ],
      examples: [
        'Legacy system modernization',
        'On-premises to cloud migration',
        'Multi-cloud strategies',
        'Cloud-native application development',
        'Infrastructure as Code (IaC)'
      ]
    },
    {
      title: 'DevOps & Automation',
      icon: '⚙️',
      description: 'Streamline development and operations with automated CI/CD pipelines and infrastructure management',
      details: [
        'CI/CD pipeline setup',
        'Infrastructure as Code (Terraform)',
        'Container orchestration (Docker/Kubernetes)',
        'Automated testing & deployment',
        'Monitoring & alerting systems',
        'Configuration management',
        'Security automation (DevSecOps)',
        'Performance monitoring'
      ],
      examples: [
        'Automated deployment pipelines',
        'Kubernetes cluster management',
        'Infrastructure provisioning',
        'Application performance monitoring',
        'Security scanning automation'
      ]
    }
  ]

  const cloudBenefits = [
    {
      icon: '💰',
      title: 'Cost Reduction',
      description: 'Reduce infrastructure costs by up to 30% with optimized cloud resource management'
    },
    {
      icon: '📈',
      title: 'Scalability',
      description: 'Scale resources up or down automatically based on demand without manual intervention'
    },
    {
      icon: '🔒',
      title: 'Enhanced Security',
      description: 'Enterprise-grade security with built-in compliance and advanced threat protection'
    },
    {
      icon: '⚡',
      title: 'Improved Performance',
      description: 'Global infrastructure ensures low latency and high availability for your applications'
    }
  ]

  const cloudProviders = [
    { name: 'Amazon AWS', icon: '🟠', description: 'Leading cloud platform with comprehensive services' },
    { name: 'Microsoft Azure', icon: '🔷', description: 'Enterprise cloud with seamless Microsoft integration' },
    { name: 'Terraform', icon: '🟣', description: 'Infrastructure as Code for multi-cloud deployments' },
    { name: 'Docker', icon: '🐳', description: 'Containerization for consistent deployments' },
    { name: 'Kubernetes', icon: '☸️', description: 'Container orchestration at scale' },
    { name: 'GitHub Actions', icon: '⚫', description: 'CI/CD automation and workflow management' }
  ]

  const migrationProcess = [
    {
      step: '1',
      title: 'Assessment & Planning',
      description: 'Comprehensive analysis of your current infrastructure and development of a tailored migration strategy.'
    },
    {
      step: '2',
      title: 'Architecture Design',
      description: 'Design cloud-native architecture optimized for performance, security, and cost-effectiveness.'
    },
    {
      step: '3',
      title: 'Migration Execution',
      description: 'Phased migration approach with minimal downtime and comprehensive testing at each stage.'
    },
    {
      step: '4',
      title: 'Optimization & Support',
      description: 'Ongoing optimization, monitoring, and support to ensure peak performance and cost efficiency.'
    }
  ]

  return (
    <div className="service-page">
      {/* Hero Section */}
      <section className="service-hero">
        <div className="container">
          <div className="breadcrumb">
            <Link to="/">Home</Link> / <span>Cloud Services</span>
          </div>
          <h1>Cloud Services (AWS & Azure)</h1>
          <p className="hero-description">
            Transform your business with enterprise-grade cloud solutions. We specialize in AWS and Microsoft Azure 
            to help you modernize infrastructure, improve scalability, and reduce operational costs while maintaining 
            the highest levels of security and performance.
          </p>
        </div>
      </section>

      {/* Services Detail Section */}
      <section className="services-detail">
        <div className="container">
          <h2>Our Cloud Services</h2>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-detail-card">
                <div className="service-header">
                  <span className="service-icon">{service.icon}</span>
                  <h3>{service.title}</h3>
                </div>
                <p className="service-description">{service.description}</p>
                
                <div className="service-content">
                  <div className="service-details">
                    <h4>What We Provide:</h4>
                    <ul>
                      {service.details.map((detail, idx) => (
                        <li key={idx}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="service-examples">
                    <h4>Use Cases:</h4>
                    <ul>
                      {service.examples.map((example, idx) => (
                        <li key={idx}>{example}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="benefits-section">
        <div className="container">
          <h2>Benefits of Cloud Migration</h2>
          <div className="benefits-grid">
            {cloudBenefits.map((benefit, index) => (
              <div key={index} className="benefit-card">
                <div className="benefit-icon">{benefit.icon}</div>
                <h3>{benefit.title}</h3>
                <p>{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="technologies-section">
        <div className="container">
          <h2>Cloud Technologies We Use</h2>
          <div className="technologies-grid">
            {cloudProviders.map((provider, index) => (
              <div key={index} className="tech-card">
                <div className="tech-icon">{provider.icon}</div>
                <h3>{provider.name}</h3>
                <p>{provider.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Migration Process Section */}
      <section className="process-section">
        <div className="container">
          <h2>Our Cloud Migration Process</h2>
          <div className="process-grid">
            {migrationProcess.map((step, index) => (
              <div key={index} className="process-step">
                <div className="step-number">{step.step}</div>
                <h3>{step.title}</h3>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <h2>Ready to Move to the Cloud?</h2>
          <p>Let's discuss how cloud services can transform your business operations and drive growth.</p>
          <div className="cta-buttons">
            <Link to="/#contact" className="btn btn-primary">Get Started</Link>
            <Link to="/" className="btn btn-secondary">Back to Home</Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default CloudServicesPage
