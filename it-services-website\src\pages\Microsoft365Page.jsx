import React from 'react'
import { Link } from 'react-router-dom'
import './ServicePage.css'

const Microsoft365Page = () => {
  const services = [
    {
      title: 'SharePoint Online',
      icon: '📊',
      description: 'Complete SharePoint solutions for document management and collaboration',
      details: [
        'Custom site design and branding',
        'Document libraries with metadata',
        'Workflow automation',
        'Permission management',
        'Search configuration',
        'Integration with other Microsoft 365 apps'
      ],
      examples: [
        'Employee intranet portals',
        'Project collaboration sites',
        'Document management systems',
        'Knowledge bases',
        'Team sites with custom workflows'
      ]
    },
    {
      title: 'Power Apps',
      icon: '⚡',
      description: 'Custom business applications without traditional coding',
      details: [
        'Canvas apps for mobile and desktop',
        'Model-driven apps for complex data',
        'Custom connectors and data sources',
        'Integration with SharePoint and Dataverse',
        'Offline capabilities',
        'User training and adoption'
      ],
      examples: [
        'Expense reporting applications',
        'Inventory management systems',
        'Customer feedback forms',
        'Asset tracking applications',
        'Employee onboarding portals'
      ]
    },
    {
      title: 'Power Automate',
      icon: '🔄',
      description: 'Workflow automation to streamline business processes',
      details: [
        'Automated approval workflows',
        'Data synchronization between systems',
        'Email and notification automation',
        'Document processing workflows',
        'Integration with 300+ connectors',
        'Scheduled and triggered flows'
      ],
      examples: [
        'Invoice approval processes',
        'New employee onboarding workflows',
        'Social media posting automation',
        'Data backup and archiving',
        'Customer service ticket routing'
      ]
    },
    {
      title: 'Microsoft Teams',
      icon: '👥',
      description: 'Enhanced collaboration and communication solutions',
      details: [
        'Custom Teams apps and tabs',
        'Bot development for automation',
        'Meeting room booking systems',
        'Integration with business applications',
        'Custom connectors and workflows',
        'Governance and compliance setup'
      ],
      examples: [
        'Project management dashboards',
        'HR helpdesk bots',
        'Sales pipeline tracking',
        'Training and onboarding channels',
        'Customer support integration'
      ]
    }
  ]

  const benefits = [
    {
      icon: '💰',
      title: 'Cost Effective',
      description: 'Leverage existing Microsoft 365 licenses to build powerful solutions without additional software costs'
    },
    {
      icon: '🔒',
      title: 'Secure & Compliant',
      description: 'Built-in security features and compliance tools to protect your business data'
    },
    {
      icon: '📈',
      title: 'Scalable',
      description: 'Solutions that grow with your business, from small teams to enterprise-wide deployments'
    },
    {
      icon: '🔗',
      title: 'Integrated',
      description: 'Seamless integration across all Microsoft 365 applications and third-party services'
    }
  ]

  return (
    <div className="service-page">
      {/* Hero Section */}
      <section className="service-hero">
        <div className="container">
          <div className="breadcrumb">
            <Link to="/">Home</Link> / <span>Microsoft 365 Assistance</span>
          </div>
          <h1>Microsoft 365 Assistance</h1>
          <p className="hero-description">
            Transform your business with comprehensive Microsoft 365 solutions. From SharePoint sites to Power Apps, 
            we help you leverage the full potential of the Microsoft ecosystem to streamline operations and boost productivity.
          </p>
        </div>
      </section>

      {/* Services Detail Section */}
      <section className="services-detail">
        <div className="container">
          <h2>Our Microsoft 365 Services</h2>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-detail-card">
                <div className="service-header">
                  <span className="service-icon">{service.icon}</span>
                  <h3>{service.title}</h3>
                </div>
                <p className="service-description">{service.description}</p>
                
                <div className="service-content">
                  <div className="service-details">
                    <h4>What We Provide:</h4>
                    <ul>
                      {service.details.map((detail, idx) => (
                        <li key={idx}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="service-examples">
                    <h4>Example Solutions:</h4>
                    <ul>
                      {service.examples.map((example, idx) => (
                        <li key={idx}>{example}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="benefits-section">
        <div className="container">
          <h2>Why Choose Microsoft 365?</h2>
          <div className="benefits-grid">
            {benefits.map((benefit, index) => (
              <div key={index} className="benefit-card">
                <div className="benefit-icon">{benefit.icon}</div>
                <h3>{benefit.title}</h3>
                <p>{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <h2>Ready to Transform Your Business?</h2>
          <p>Let's discuss how Microsoft 365 can streamline your operations and boost productivity.</p>
          <div className="cta-buttons">
            <Link to="/#contact" className="btn btn-primary">Get Started</Link>
            <Link to="/" className="btn btn-secondary">Back to Home</Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Microsoft365Page
