.service-page {
  padding-top: 80px; /* Account for fixed header */
}

/* Hero Section */
.service-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 80px 0;
  text-align: center;
}

.breadcrumb {
  margin-bottom: 20px;
  font-size: 1rem;
}

.breadcrumb a {
  color: #ffd700;
  text-decoration: none;
}

.breadcrumb span {
  color: white;
}

.service-hero h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-description {
  font-size: 1.3rem;
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.9;
  line-height: 1.6;
}

/* Services Detail Section */
.services-detail {
  padding: 80px 0;
  background: #f8f9fa;
}

.services-detail h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 40px;
}

.service-detail-card {
  background: white;
  padding: 40px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.service-detail-card:hover {
  transform: translateY(-5px);
}

.service-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
}

.service-icon {
  font-size: 3rem;
}

.service-header h3 {
  font-size: 1.8rem;
  color: #2c3e50;
  margin: 0;
}

.service-description {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
}

.service-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.service-details h4,
.service-examples h4 {
  color: #007bff;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.service-details ul,
.service-examples ul {
  list-style: none;
  padding: 0;
}

.service-details li,
.service-examples li {
  padding: 8px 0;
  color: #555;
  position: relative;
  padding-left: 25px;
}

.service-details li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.service-examples li::before {
  content: '→';
  position: absolute;
  left: 0;
  color: #007bff;
  font-weight: bold;
}

/* Benefits Section */
.benefits-section {
  padding: 80px 0;
  background: white;
}

.benefits-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.benefit-card {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 15px;
  transition: transform 0.3s ease;
}

.benefit-card:hover {
  transform: translateY(-5px);
}

.benefit-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.benefit-card h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.benefit-card p {
  color: #666;
  line-height: 1.6;
}

/* Technologies Section */
.technologies-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.technologies-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.technologies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.tech-card {
  background: white;
  padding: 30px 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;
}

.tech-card:hover {
  transform: translateY(-5px);
}

.tech-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
}

.tech-card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.tech-card p {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Process Section */
.process-section {
  padding: 80px 0;
  background: white;
}

.process-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.process-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.process-step {
  text-align: center;
  padding: 30px 20px;
}

.step-number {
  width: 60px;
  height: 60px;
  background: #007bff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  margin: 0 auto 20px;
}

.process-step h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.3rem;
}

.process-step p {
  color: #666;
  line-height: 1.6;
}

/* Use Cases Section */
.use-cases-section {
  padding: 80px 0;
  background: white;
}

.use-cases-section h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 60px;
  color: #2c3e50;
}

.use-cases-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.use-case-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
}

.use-case-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.use-case-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.4rem;
}

.use-case-card ul {
  list-style: none;
  padding: 0;
  text-align: left;
}

.use-case-card li {
  padding: 5px 0;
  color: #555;
  position: relative;
  padding-left: 20px;
}

.use-case-card li::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #007bff;
  font-weight: bold;
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.cta-buttons .btn {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: bold;
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #ffd700;
  color: #333;
}

.btn-primary:hover {
  background: #ffed4e;
  transform: translateY(-2px);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn-secondary:hover {
  background: white;
  color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
  .service-hero h1 {
    font-size: 2.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }

  .service-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .service-detail-card {
    padding: 30px 20px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
}
