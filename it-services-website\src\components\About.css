.about {
  background: white;
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 60px;
  align-items: start;
}

.about-text .section-title {
  text-align: left;
  margin-bottom: 30px;
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #4a4a4a;
  margin-bottom: 25px;
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.expertise-item {
  padding: 25px;
  background: #f5f5f5;
  border-radius: 10px;
  border-left: 4px solid #2c2c2c;
}

.expertise-item h4 {
  color: #1a1a1a;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.expertise-item p {
  color: #5a5a5a;
  line-height: 1.6;
}

.about-stats {
  background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 50%, #4a4a4a 100%);
  color: white;
  padding: 40px 30px;
  border-radius: 15px;
  text-align: center;
  position: sticky;
  top: 100px;
}

.about-stats h3 {
  font-size: 1.8rem;
  margin-bottom: 30px;
  color: #c0c0c0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #c0c0c0;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .about-stats {
    position: static;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .expertise-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .stat-number {
    font-size: 2rem;
  }

  .about-stats {
    padding: 30px 20px;
  }
}
