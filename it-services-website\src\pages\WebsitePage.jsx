import React from 'react'
import { Link } from 'react-router-dom'
import './ServicePage.css'

const WebsitePage = () => {
  const services = [
    {
      title: 'Responsive Web Design',
      icon: '📱',
      description: 'Modern, mobile-first websites that work perfectly on all devices',
      details: [
        'Mobile-first design approach',
        'Cross-browser compatibility',
        'Fast loading times',
        'SEO-optimized structure',
        'Accessibility compliance',
        'Modern UI/UX design'
      ],
      examples: [
        'Corporate business websites',
        'Professional portfolios',
        'Landing pages for campaigns',
        'Service-based business sites',
        'Non-profit organization websites'
      ]
    },
    {
      title: 'E-commerce Solutions',
      icon: '🛒',
      description: 'Complete online stores with payment processing and inventory management',
      details: [
        'Shopping cart functionality',
        'Payment gateway integration',
        'Inventory management',
        'Order tracking systems',
        'Customer account portals',
        'Analytics and reporting'
      ],
      examples: [
        'Online retail stores',
        'Digital product marketplaces',
        'Subscription-based services',
        'B2B wholesale platforms',
        'Multi-vendor marketplaces'
      ]
    },
    {
      title: 'Content Management Systems',
      icon: '📝',
      description: 'Easy-to-use systems for managing your website content',
      details: [
        'User-friendly admin interfaces',
        'Content scheduling and publishing',
        'Multi-user access controls',
        'Media library management',
        'SEO tools integration',
        'Backup and security features'
      ],
      examples: [
        'Blog and news websites',
        'Corporate intranets',
        'Educational platforms',
        'Community forums',
        'Documentation sites'
      ]
    },
    {
      title: 'Web Applications',
      icon: '⚙️',
      description: 'Custom web applications tailored to your business needs',
      details: [
        'Custom functionality development',
        'Database integration',
        'User authentication systems',
        'API development and integration',
        'Real-time features',
        'Cloud deployment'
      ],
      examples: [
        'Customer relationship management',
        'Project management tools',
        'Booking and scheduling systems',
        'Data visualization dashboards',
        'Collaboration platforms'
      ]
    }
  ]

  const technologies = [
    { name: 'React', icon: '⚛️', description: 'Modern JavaScript framework for interactive UIs' },
    { name: 'Next.js', icon: '🔺', description: 'Full-stack React framework with SSR' },
    { name: 'WordPress', icon: '📰', description: 'Popular CMS for content-driven sites' },
    { name: 'Shopify', icon: '🛍️', description: 'Leading e-commerce platform' },
    { name: 'Node.js', icon: '🟢', description: 'Server-side JavaScript runtime' },
    { name: 'Python/Django', icon: '🐍', description: 'Robust backend development' }
  ]

  const process = [
    {
      step: '1',
      title: 'Discovery & Planning',
      description: 'We analyze your requirements, target audience, and business goals to create a comprehensive project plan.'
    },
    {
      step: '2',
      title: 'Design & Prototyping',
      description: 'Our designers create wireframes and mockups to visualize your website before development begins.'
    },
    {
      step: '3',
      title: 'Development & Testing',
      description: 'We build your website using modern technologies and thoroughly test across all devices and browsers.'
    },
    {
      step: '4',
      title: 'Launch & Support',
      description: 'We deploy your website and provide ongoing maintenance, updates, and technical support.'
    }
  ]

  return (
    <div className="service-page">
      {/* Hero Section */}
      <section className="service-hero">
        <div className="container">
          <div className="breadcrumb">
            <Link to="/">Home</Link> / <span>Website Building Services</span>
          </div>
          <h1>Website Building Services</h1>
          <p className="hero-description">
            Create a powerful online presence with our professional website development services. 
            From simple business websites to complex e-commerce platforms, we build solutions that drive results.
          </p>
        </div>
      </section>

      {/* Services Detail Section */}
      <section className="services-detail">
        <div className="container">
          <h2>Our Website Development Services</h2>
          <div className="services-grid">
            {services.map((service, index) => (
              <div key={index} className="service-detail-card">
                <div className="service-header">
                  <span className="service-icon">{service.icon}</span>
                  <h3>{service.title}</h3>
                </div>
                <p className="service-description">{service.description}</p>
                
                <div className="service-content">
                  <div className="service-details">
                    <h4>Features Included:</h4>
                    <ul>
                      {service.details.map((detail, idx) => (
                        <li key={idx}>{detail}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="service-examples">
                    <h4>Perfect For:</h4>
                    <ul>
                      {service.examples.map((example, idx) => (
                        <li key={idx}>{example}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="technologies-section">
        <div className="container">
          <h2>Technologies We Use</h2>
          <div className="technologies-grid">
            {technologies.map((tech, index) => (
              <div key={index} className="tech-card">
                <div className="tech-icon">{tech.icon}</div>
                <h3>{tech.name}</h3>
                <p>{tech.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="process-section">
        <div className="container">
          <h2>Our Development Process</h2>
          <div className="process-grid">
            {process.map((step, index) => (
              <div key={index} className="process-step">
                <div className="step-number">{step.step}</div>
                <h3>{step.title}</h3>
                <p>{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta-section">
        <div className="container">
          <h2>Ready to Build Your Website?</h2>
          <p>Let's create a website that represents your brand and drives business growth.</p>
          <div className="cta-buttons">
            <Link to="/#contact" className="btn btn-primary">Start Your Project</Link>
            <Link to="/" className="btn btn-secondary">Back to Home</Link>
          </div>
        </div>
      </section>
    </div>
  )
}

export default WebsitePage
