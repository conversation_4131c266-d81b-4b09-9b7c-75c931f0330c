import React from 'react'
import './About.css'

const About = () => {
  const stats = [
    { number: '50+', label: 'Projects Completed' },
    { number: '25+', label: 'Happy Clients' },
    { number: '5+', label: 'Years Experience' },
    { number: '24/7', label: 'Support Available' }
  ]

  const expertise = [
    {
      title: 'Microsoft 365 Expertise',
      description: 'Deep knowledge of SharePoint Online, Power Platform, and Microsoft 365 ecosystem'
    },
    {
      title: 'Custom Development',
      description: 'Tailored solutions built to meet your specific business requirements'
    },
    {
      title: 'AI & Automation',
      description: 'Cutting-edge AI solutions to streamline your business processes'
    },
    {
      title: 'Ongoing Support',
      description: 'Comprehensive support and maintenance for all our solutions'
    }
  ]

  return (
    <section id="about" className="about section">
      <div className="container">
        <div className="about-content">
          <div className="about-text">
            <h2 className="section-title">About BossKing Technologies</h2>
            <p className="about-description">
              We are a leading IT services company specializing in modern business solutions. 
              Our team of experienced professionals is dedicated to helping businesses leverage 
              technology to improve efficiency, reduce costs, and drive growth.
            </p>
            <p className="about-description">
              With deep expertise in the complete Microsoft 365 ecosystem (including SharePoint Online, Power Apps,
              Power Automate, and Teams), web development, and AI automation, we provide comprehensive solutions
              that transform how businesses operate. Our commitment to excellence and customer satisfaction has
              made us a trusted partner for organizations of all sizes.
            </p>
            
            <div className="expertise-grid">
              {expertise.map((item, index) => (
                <div key={index} className="expertise-item">
                  <h4>{item.title}</h4>
                  <p>{item.description}</p>
                </div>
              ))}
            </div>
          </div>
          
          <div className="about-stats">
            <h3>Our Track Record</h3>
            <div className="stats-grid">
              {stats.map((stat, index) => (
                <div key={index} className="stat-item">
                  <div className="stat-number">{stat.number}</div>
                  <div className="stat-label">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
