import React from 'react'
import './Services.css'

const Services = () => {
  const services = [
    {
      icon: '📊',
      title: 'SharePoint Online Services',
      description: 'Complete SharePoint Online solutions including site creation, customization, document management, and workflow automation to streamline your business processes.',
      features: [
        'Site Design & Development',
        'Document Management Systems',
        'Workflow Automation',
        'Custom Lists & Libraries',
        'Permission Management',
        'Integration with Microsoft 365'
      ]
    },
    {
      icon: '⚡',
      title: 'Power Apps Development',
      description: 'Custom Power Apps solutions to digitize your business processes, create mobile-friendly applications, and connect your data across multiple platforms.',
      features: [
        'Canvas & Model-driven Apps',
        'Custom Business Applications',
        'Data Integration',
        'Mobile-Responsive Design',
        'Power Automate Integration',
        'User Training & Support'
      ]
    },
    {
      icon: '🌐',
      title: 'Website Building Services',
      description: 'Professional website development services from simple business websites to complex web applications, all optimized for performance and user experience.',
      features: [
        'Responsive Web Design',
        'E-commerce Solutions',
        'Content Management Systems',
        'SEO Optimization',
        'Performance Optimization',
        'Maintenance & Support'
      ]
    },
    {
      icon: '🤖',
      title: 'AI Automation Solutions',
      description: 'Leverage artificial intelligence to automate repetitive tasks, improve decision-making, and enhance customer experiences with intelligent automation.',
      features: [
        'Process Automation',
        'Chatbot Development',
        'Data Analysis & Insights',
        'Predictive Analytics',
        'Document Processing',
        'Custom AI Solutions'
      ]
    }
  ]

  return (
    <section id="services" className="services section">
      <div className="container">
        <h2 className="section-title">Our IT Services</h2>
        <p className="section-subtitle">
          Comprehensive technology solutions designed to transform your business operations and drive growth
        </p>
        
        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">{service.icon}</div>
              <h3 className="service-title">{service.title}</h3>
              <p className="service-description">{service.description}</p>
              <ul className="service-features">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex}>{feature}</li>
                ))}
              </ul>
              <button className="btn service-btn">Learn More</button>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
