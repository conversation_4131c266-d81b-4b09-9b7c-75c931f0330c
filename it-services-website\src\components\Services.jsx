import React from 'react'
import { Link } from 'react-router-dom'
import './Services.css'

const Services = () => {
  const services = [
    {
      icon: '🏢',
      title: 'Microsoft 365 Assistance',
      description: 'Comprehensive Microsoft 365 solutions including SharePoint Online, Power Apps, Power Automate, Teams, and complete ecosystem integration to transform your business operations.',
      features: [
        'SharePoint Site Design & Development',
        'Power Apps (Canvas & Model-driven)',
        'Power Automate Workflows',
        'Microsoft Teams Integration',
        'Document Management Systems',
        'User Training & Support',
        'Microsoft 365 Migration',
        'Security & Compliance Setup'
      ],
      link: '/services/microsoft365'
    },
    {
      icon: '🌐',
      title: 'Website Building Services',
      description: 'Professional website development services from simple business websites to complex web applications, all optimized for performance and user experience.',
      features: [
        'Responsive Web Design',
        'E-commerce Solutions',
        'Content Management Systems',
        'SEO Optimization',
        'Performance Optimization',
        'Maintenance & Support'
      ],
      link: '/services/website-building'
    },
    {
      icon: '🤖',
      title: 'AI Automation Solutions',
      description: 'Leverage artificial intelligence to automate repetitive tasks, improve decision-making, and enhance customer experiences with intelligent automation.',
      features: [
        'Process Automation',
        'Chatbot Development',
        'Data Analysis & Insights',
        'Predictive Analytics',
        'Document Processing',
        'Custom AI Solutions'
      ],
      link: '/services/ai-automation'
    }
  ]

  return (
    <section id="services" className="services section">
      <div className="container">
        <h2 className="section-title">Our IT Services</h2>
        <p className="section-subtitle">
          Comprehensive technology solutions designed to transform your business operations and drive growth
        </p>
        
        <div className="services-grid">
          {services.map((service, index) => (
            <div key={index} className="service-card">
              <div className="service-icon">{service.icon}</div>
              <h3 className="service-title">{service.title}</h3>
              <p className="service-description">{service.description}</p>
              <ul className="service-features">
                {service.features.map((feature, featureIndex) => (
                  <li key={featureIndex}>{feature}</li>
                ))}
              </ul>
              <Link to={service.link} className="btn service-btn">Learn More</Link>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Services
