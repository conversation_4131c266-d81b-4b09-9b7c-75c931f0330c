/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.App {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #2c2c2c;
}

/* Utility Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  background: #2c2c2c;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.btn:hover {
  background: #000;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c6c6c;
}

.btn-secondary:hover {
  background: #4a4a4a;
}

.section {
  padding: 80px 0;
}

.section-title {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.section-subtitle {
  text-align: center;
  font-size: 1.2rem;
  color: #5a5a5a;
  margin-bottom: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}
