.services {
  background: #f5f5f5;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.service-card {
  background: white;
  padding: 40px 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2c2c2c, #1a1a1a);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-card:hover::before {
  transform: scaleX(1);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-icon {
  font-size: 4rem;
  margin-bottom: 25px;
  display: block;
}

.service-title {
  font-size: 1.5rem;
  color: #1a1a1a;
  margin-bottom: 20px;
  font-weight: bold;
}

.service-description {
  color: #5a5a5a;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 1rem;
}

.service-features {
  list-style: none;
  text-align: left;
  margin-bottom: 30px;
}

.service-features li {
  padding: 8px 0;
  color: #4a4a4a;
  position: relative;
  padding-left: 25px;
}

.service-features li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #6c6c6c;
  font-weight: bold;
}

.service-btn {
  background: #2c2c2c;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
}

.service-btn:hover {
  background: #000;
  transform: translateY(-2px);
  color: white;
}

@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .service-card {
    padding: 30px 20px;
  }

  .service-icon {
    font-size: 3rem;
  }

  .service-title {
    font-size: 1.3rem;
  }
}
